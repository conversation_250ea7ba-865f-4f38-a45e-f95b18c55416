#!/usr/bin/env python3
"""
Test script to verify amount extraction logic is working
"""

import re
import logging

# Set up logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')

def safe_float_conversion(value):
    """Safely convert a value to float, handling various formats."""
    if value is None:
        return None
    
    if isinstance(value, (int, float)):
        return float(value)
    
    if isinstance(value, str):
        # Remove common formatting characters
        cleaned = value.strip().replace(',', '').replace('₹', '').replace('Rs.', '').replace('INR', '')
        
        # Handle negative values
        is_negative = cleaned.startswith('-') or cleaned.startswith('(')
        cleaned = cleaned.replace('-', '').replace('(', '').replace(')', '')
        
        try:
            result = float(cleaned)
            return -result if is_negative else result
        except ValueError:
            return None
    
    return None

def test_amount_extraction():
    """Test the amount extraction logic with sample descriptions"""
    
    # Sample descriptions from the bank statement
    test_descriptions = [
        "UPI~************~**********@okbizaxis ~Nidaan Pharm -YBLe918bed0d05441009d3d8ec4ac3 UPI~************~paytmqr2810050501011 60umf96ds91@p -YBLdf889150238b4e0f83034d82d74",
        "UPI~************~*********@ybl~DK Salon -YBLbb7fe3ca0640449c9cff16e12f7 IMPS-************-**********-KALYANE E CHETIA -HDFCBANKLTD -From Loni Beng UPI~************~**********@okbizaxis~ Nidaan Pharm -YBLfd8fae68178f499ca23fb29ba5e",
        "Purchase-************-130121 13:40:41-RELIANCE FRE -463237XXXXXX2723-258637",
        "ATM WITHDRAWAL -DPRH3973-NIPANIA INDORE -463237XXXXXX2723-********",
        "NEFT-0811OP1008156394-Generx Pharma -ICICI BANK LIMITED-ICIC0000709",
        "UPI~************~BHARATPE.********** @fbpe~DILIP -YBLd29201df2dab40d0a630e587088"
    ]
    
    for i, description in enumerate(test_descriptions):
        print(f"\n--- Testing Description {i+1} ---")
        print(f"Description: {description}")
        
        # Test currency patterns
        currency_patterns = [
            r'Rs\.?\s*(\d+(?:,\d{3})*(?:\.\d{2})?)',  # Rs. 1500.00 or Rs. 1,000.00
            r'INR\s*(\d+(?:,\d{3})*(?:\.\d{2})?)',   # INR 1500.00 or INR 1,000.00
            r'₹\s*(\d+(?:,\d{3})*(?:\.\d{2})?)'      # ₹ 1500.00 or ₹ 1,000.00
        ]
        
        amount_found = False
        amount = None
        for pattern in currency_patterns:
            matches = re.findall(pattern, description)
            if matches:
                amount_str = matches[-1].replace(',', '')  # Take the last match
                amount = safe_float_conversion(amount_str)
                if amount is not None and amount > 0 and amount < 10000000:  # Reasonable amount limit
                    print(f"Found currency amount: {amount} using pattern: {pattern}")
                    amount_found = True
                    break
        
        # If no currency pattern found, look for amounts at the end of the description
        if not amount_found:
            end_amount_patterns = [
                r'\s(\d+(?:,\d{3})*\.\d{2})$',  # 1500.00 at end or 1,000.00 at end
                r'\s(\d{3,6})$',  # 100-999999 at end (reasonable range, avoid small numbers like IDs)
                r'\s(\d+(?:,\d{3})*\.\d{2})\s*$',  # 1500.00 near end or 1,000.00 near end
            ]
            
            for pattern in end_amount_patterns:
                matches = re.findall(pattern, description)
                if matches:
                    amount_str = matches[-1].replace(',', '')  # Take the last match
                    amount = safe_float_conversion(amount_str)
                    if amount is not None and amount > 0 and amount < 10000000:  # Reasonable amount limit
                        print(f"Found end amount: {amount} using pattern: {pattern}")
                        amount_found = True
                        break
        
        if amount_found and amount is not None:
            # Determine if it's debit or credit based on keywords
            desc_lower = description.lower()
            if any(keyword in desc_lower for keyword in ['debit', 'withdrawal', 'dr', 'paid', 'transfer']):
                print(f"Classified as DEBIT: {amount}")
            elif any(keyword in desc_lower for keyword in ['credit', 'deposit', 'cr', 'received', 'salary']):
                print(f"Classified as CREDIT: {amount}")
            else:
                # Default to credit for UPI transactions (common pattern)
                if 'upi' in desc_lower:
                    print(f"Classified as UPI CREDIT: {amount}")
                else:
                    print(f"Classified as DEFAULT DEBIT: {amount}")
        else:
            print("No amount found in description")

if __name__ == "__main__":
    test_amount_extraction()
