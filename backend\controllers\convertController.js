const asyncHandler = require('../middleware/asyncHandler');
const pdfParse = require('pdf-parse');
const fs = require('fs');
const path = require('path');
const { processPdfWithTesseract, extractGridsFromPdf, customRenderPage } = require('../utils/pdfUtils');
// const User = require('../models/User'); // Temporarily disable User model

// Custom render function to better preserve table structure

// @desc    Handle PDF file upload and immediate processing
// @route   POST /api/convert/upload
// @access  Public (or Private depending on route setup)
exports.uploadStatement = asyncHandler(async (req, res, next) => {
  if (!req.file) {
    return res.status(400).json({ success: false, message: 'No file uploaded.' });
  }

  // // Check if user is authenticated and their conversion limit (TEMPORARILY DISABLED)
  // if (req.user) {
  //   const user = await User.findById(req.user.id);
  //   if (user.tier === 'registered' && user.conversionCount >= 5) {
  //     fs.unlinkSync(req.file.path); // Clean up file
  //     return res.status(403).json({
  //       success: false,
  //       message: 'Monthly conversion limit reached. Upgrade to premium for unlimited conversions.'
  //     });
  //   }
  // } else {
  //   // Anonymous user rate limiting could be added here
  // }

  try {
    console.log("Starting PDF processing...");

    // Use our Python-based PDF table extractor
    try {
      console.log("Attempting Python-based PDF table extraction...");
      const { spawn } = require('child_process');

      // Call our Python script
      const pythonProcess = spawn('python', [
        path.join(__dirname, '..', 'pdf_table_extractor.py'),
        'convert',
        req.file.path
      ]);

      let pythonOutput = '';
      let pythonError = '';

      pythonProcess.stdout.on('data', (data) => {
        pythonOutput += data.toString();
      });

      pythonProcess.stderr.on('data', (data) => {
        pythonError += data.toString();
      });

      // Wait for Python process to complete
      const pythonResult = await new Promise((resolve, reject) => {
        pythonProcess.on('close', (code) => {
          if (code === 0) {
            try {
              const result = JSON.parse(pythonOutput);
              resolve(result);
            } catch (parseError) {
              console.error("Failed to parse Python output:", pythonOutput);
              reject(new Error(`Failed to parse Python output: ${parseError.message}`));
            }
          } else {
            console.error("Python script failed:", pythonError);
            reject(new Error(`Python script failed with code ${code}: ${pythonError}`));
          }
        });
      });

      if (pythonResult && pythonResult.tables_data && pythonResult.tables_data.length > 0) {
        console.log(`Successfully extracted ${pythonResult.tables_data.length} tables using Python extraction`);

        // Convert the extracted tables to the expected transaction format
        const transactions = convertPythonTablesToTransactions(pythonResult.tables_data);

        if (transactions.length > 0) {
          // Clean up the file after processing
          fs.unlinkSync(req.file.path);

          console.log(`Converted to ${transactions.length} transactions`);
          return res.status(200).json({
            success: true,
            count: transactions.length,
            data: transactions,
            originalFilename: req.file.originalname
          });
        }
      }
    } catch (pythonError) {
      console.error("Python-based extraction failed:", pythonError);
      // Continue to fallback method
    }

    // Fallback to traditional PDF parsing if grid extraction failed
    console.log("Falling back to traditional PDF parsing...");
    const dataBuffer = fs.readFileSync(req.file.path);

    const options = {
      pagerender: customRenderPage,
      max: 0 // Process all pages
    };

    const data = await pdfParse(dataBuffer, options);
    console.log("PDF Parsed Successfully. Extracting text..."); // Add logging

    // // Update user's conversion count if authenticated (TEMPORARILY DISABLED)
    // if (req.user) {
    //   await User.findByIdAndUpdate(req.user.id, {
    //     $inc: { conversionCount: 1 }
    //   });
    // }

    // Try to detect table structure
    const lines = data.text.split('\n').filter(line => line.trim() !== '');
    let transactions = [];

    // First, try to detect if this is a tabular format with consistent columns
    // Look for patterns in the data to identify column positions

    // Common date patterns
    const datePatterns = [
      /\d{2}\/\d{2}\/\d{4}/, // DD/MM/YYYY
      /\d{2}-\d{2}-\d{4}/, // DD-MM-YYYY
      /\d{4}\/\d{2}\/\d{2}/, // YYYY/MM/DD
      /\d{4}-\d{2}-\d{2}/, // YYYY-MM-DD
      /\d{1,2}\s+[A-Za-z]{3}\s+\d{4}/, // D MMM YYYY
      /[A-Za-z]{3}\s+\d{1,2},?\s+\d{4}/ // MMM D, YYYY
    ];

    // Money amount patterns
    const amountPattern = /[\d,]*\.\d+/g;

    // Try to identify header row
    let headerRow = -1;
    const headerKeywords = ['date', 'description', 'transaction', 'amount', 'balance', 'deposit', 'withdrawal', 'debit', 'credit', 'particulars', 'details'];

    for (let i = 0; i < Math.min(20, lines.length); i++) {
      const line = lines[i].toLowerCase();
      const keywordMatches = headerKeywords.filter(keyword => line.includes(keyword)).length;
      if (keywordMatches >= 2) { // Require at least 2 keywords for better accuracy
        headerRow = i;
        break;
      }
    }

    // If we found a header, try to extract data based on column positions
    if (headerRow >= 0) {
      const headerLine = lines[headerRow].toLowerCase();
      // Try both tab and space-based column detection
      let columns = headerLine.split('\t');

      // If not enough columns with tabs, try space-based splitting with position analysis
      if (columns.length < 3) {
        // Analyze positions of keywords to determine column boundaries
        const positions = [];
        headerKeywords.forEach(keyword => {
          const pos = headerLine.indexOf(keyword);
          if (pos >= 0) {
            positions.push({ keyword, pos });
          }
        });

        // Sort positions
        positions.sort((a, b) => a.pos - b.pos);

        // Use positions to split lines
        if (positions.length >= 2) {
          columns = positions.map(p => p.keyword);
        } else {
          // Fallback to simple space-based splitting
          columns = headerLine.split(/\s{2,}/);
        }
      }

      // Identify column indices
      let dateCol = -1;
      let descCol = -1;
      let withdrawalCol = -1;
      let depositCol = -1;
      let balanceCol = -1;

      columns.forEach((col, index) => {
        if (col.includes('date')) dateCol = index;
        if (col.includes('description') || col.includes('transaction') || col.includes('details') || col.includes('particulars')) descCol = index;
        if (col.includes('withdrawal') || col.includes('debit') || col.includes('out') || col.includes('dr')) withdrawalCol = index;
        if (col.includes('deposit') || col.includes('credit') || col.includes('in') || col.includes('cr')) depositCol = index;
        if (col.includes('balance')) balanceCol = index;
      });

      console.log(`Identified columns - Date: ${dateCol}, Desc: ${descCol}, Withdrawal: ${withdrawalCol}, Deposit: ${depositCol}, Balance: ${balanceCol}`);

      // Extract data from rows
      for (let i = headerRow + 1; i < lines.length; i++) {
        // Try both tab and space-based splitting
        let cols = lines[i].split('\t');
        if (cols.length < 3) {
          // Try space-based splitting with position analysis
          cols = lines[i].split(/\s{2,}/);
        }

        if (cols.length < 2) continue; // Skip lines that don't have enough columns

        // Check if this row contains a date
        let hasDate = false;
        let dateValue = '';

        if (dateCol >= 0 && dateCol < cols.length) {
          for (const pattern of datePatterns) {
            const match = cols[dateCol].match(pattern);
            if (match) {
              hasDate = true;
              dateValue = match[0];
              break;
            }
          }
        } else {
          // If date column not identified, check all columns
          for (const col of cols) {
            for (const pattern of datePatterns) {
              const match = col.match(pattern);
              if (match) {
                hasDate = true;
                dateValue = match[0];
                break;
              }
            }
            if (hasDate) break;
          }
        }

        if (!hasDate) continue; // Skip rows without dates

        const transaction = {
          date: dateValue || (dateCol >= 0 && dateCol < cols.length ? cols[dateCol].trim() : ''),
          description: descCol >= 0 && descCol < cols.length ? cols[descCol].trim() : '',
          withdrawal: null,
          deposit: null,
          balance: null
        };

        // Extract amounts - look for currency patterns in all columns if specific columns not identified
        if (withdrawalCol >= 0 && withdrawalCol < cols.length) {
          const match = cols[withdrawalCol].match(amountPattern);
          if (match) transaction.withdrawal = parseFloat(match[0].replace(/,/g, ''));
        }

        if (depositCol >= 0 && depositCol < cols.length) {
          const match = cols[depositCol].match(amountPattern);
          if (match) transaction.deposit = parseFloat(match[0].replace(/,/g, ''));
        }

        if (balanceCol >= 0 && balanceCol < cols.length) {
          const match = cols[balanceCol].match(amountPattern);
          if (match) transaction.balance = parseFloat(match[0].replace(/,/g, ''));
        }

        // If specific columns weren't identified, try to find amounts in any column
        if (!transaction.withdrawal && !transaction.deposit) {
          for (const col of cols) {
            const match = col.match(amountPattern);
            if (match) {
              // If we already have a balance but no withdrawal/deposit, assume this is one of those
              if (!transaction.balance) {
                transaction.balance = parseFloat(match[0].replace(/,/g, ''));
              } else if (!transaction.withdrawal) {
                transaction.withdrawal = parseFloat(match[0].replace(/,/g, ''));
              } else if (!transaction.deposit) {
                transaction.deposit = parseFloat(match[0].replace(/,/g, ''));
              }
            }
          }
        }

        transactions.push(transaction);
      }
    }

    // If table structure detection failed, fall back to regex pattern matching
    if (transactions.length === 0) {
      console.log("Table structure detection failed, falling back to regex pattern...");

      // Try multiple transaction patterns for different bank formats
      const transactionPatterns = [
        // Standard format: date, description, amounts
        /(\d{2}\/\d{2}\/\d{4})\s+(.*?)\s+([\d,]*\.\d+)?\s+([\d,]*\.\d+)?\s+([\d,]*\.\d+)?/g,
        // Alternative date format
        /(\d{2}-\d{2}-\d{4})\s+(.*?)\s+([\d,]*\.\d+)?\s+([\d,]*\.\d+)?\s+([\d,]*\.\d+)/g,
        // Date with month name
        /(\d{1,2}\s+[A-Za-z]{3}\s+\d{4})\s+(.*?)\s+([\d,]*\.\d+)?\s+([\d,]*\.\d+)?\s+([\d,]*\.\d+)/g
      ];

      for (const pattern of transactionPatterns) {
        let match;
        while ((match = pattern.exec(data.text)) !== null) {
          transactions.push({
            date: match[1],
            description: match[2].trim(),
            withdrawal: match[3] ? parseFloat(match[3].replace(/,/g, '')) : null,
            deposit: match[4] ? parseFloat(match[4].replace(/,/g, '')) : null,
            balance: match[5] ? parseFloat(match[5].replace(/,/g, '')) : null
          });
        }

        // If we found transactions with this pattern, stop trying others
        if (transactions.length > 0) {
          break;
        }
      }
    }

    // If no transactions were found, create a simple data structure from the text
    if (transactions.length === 0) {
      console.log("No structured transactions found, creating generic data...");

      // Split the text into lines and create simple rows
      const lines = data.text.split('\n')
        .filter(line => line.trim().length > 0)
        .slice(0, 20); // Limit to first 20 lines for simplicity

      if (lines.length > 0) {
        transactions = lines.map((line, index) => ({
          date: new Date().toLocaleDateString(), // Current date as placeholder
          description: line.trim(),
          withdrawal: null,
          deposit: null,
          balance: null
        }));
      }
    }

    // Clean up the file after processing
    fs.unlinkSync(req.file.path);

    if (transactions.length === 0) {
      console.log(`No content found in the PDF.`); // Add logging
      return res.status(400).json({
        success: false,
        message: 'No content found in the PDF. Please try a different file or use the manual extraction option.'
      });
    }

    console.log(`Found ${transactions.length} transactions.`); // Add logging
    res.status(200).json({
      success: true,
      count: transactions.length,
      data: transactions,
      originalFilename: req.file.originalname
    });

  } catch (error) {
    console.error("Error processing PDF:", error);
    // Clean up file if it exists
    if (fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    res.status(500).json({
      success: false,
      message: 'Error processing PDF',
      error: error.message
    });
  }
});

/**
 * Convert extracted tables to transaction format
 * @param {Array} tables - Array of tables extracted from PDF
 * @returns {Array} - Array of transactions in standard format
 */
const convertTablesToTransactions = (tables) => {
  if (!tables || tables.length === 0) {
    return [];
  }

  const transactions = [];

  tables.forEach(table => {
    if (!table.rows || table.rows.length === 0) {
      return;
    }

    // Map headers to indices (case-insensitive, fuzzy)
    const headerMap = {};
    if (table.columns || table.headers) {
      const headers = table.columns || table.headers;
      headers.forEach((h, idx) => {
        const name = String(h).toLowerCase().replace(/[^a-z]/g, '');
        if (name.includes('date') && !headerMap.date) headerMap.date = idx;
        else if (name.includes('valuedate') && !headerMap.valuedate) headerMap.valuedate = idx;
        else if (name.includes('desc') || name.includes('particular') || name.includes('details')) headerMap.description = idx;
        else if (name.includes('debit') || name.includes('withdrawal') || name.includes('dr')) headerMap.debit = idx;
        else if (name.includes('credit') || name.includes('deposit') || name.includes('cr')) headerMap.credit = idx;
        else if (name.includes('balance')) headerMap.balance = idx;
      });
    }

    table.rows.forEach(row => {
      // Skip empty rows
      if (!row || row.every(cell => !cell || String(cell).trim() === '')) return;

      // Extract by header mapping
      const get = (key) => headerMap[key] !== undefined && row[headerMap[key]] !== undefined ? String(row[headerMap[key]]).trim() : '';
      const date = get('date');
      const valuedate = get('valuedate');
      const description = get('description');
      const debitRaw = get('debit');
      const creditRaw = get('credit');
      const balanceRaw = get('balance');

      // Only one of debit/credit should be filled
      let debit = debitRaw && debitRaw.match(/\d/) ? parseFloat(debitRaw.replace(/,/g, '')) : null;
      let credit = creditRaw && creditRaw.match(/\d/) ? parseFloat(creditRaw.replace(/,/g, '')) : null;
      if (debit && credit) {
        // If both are filled, keep the non-zero or larger one, set the other to null
        if (Math.abs(debit) >= Math.abs(credit)) credit = null;
        else debit = null;
      }
      if (!debit && !credit) {
        // If neither, leave both null
        debit = null;
        credit = null;
      }
      const balance = balanceRaw && balanceRaw.match(/\d/) ? parseFloat(balanceRaw.replace(/,/g, '')) : null;

      // At least one of date, description, debit, credit, or balance must be present
      if (date || description || debit !== null || credit !== null || balance !== null) {
        transactions.push({
          date,
          valuedate,
          description,
          debit,
          credit,
          balance
        });
      }
    });
  });

  return transactions;
};

/**
 * Convert Python-extracted tables to transaction format
 * @param {Array} tables_data - Array of tables extracted from Python script
 * @returns {Array} - Array of transactions in standard format
 */
const convertPythonTablesToTransactions = (tables_data) => {
  if (!tables_data || tables_data.length === 0) {
    return [];
  }

  const transactions = [];

  tables_data.forEach(table => {
    if (!table.rows || table.rows.length === 0) {
      return;
    }

    // Map headers to indices (case-insensitive, fuzzy)
    const headerMap = {};
    if (table.headers) {
      table.headers.forEach((h, idx) => {
        const name = String(h).toLowerCase().replace(/[^a-z]/g, '');
        if (name.includes('date') && !headerMap.date) headerMap.date = idx;
        else if (name.includes('valuedate') && !headerMap.valuedate) headerMap.valuedate = idx;
        else if (name.includes('desc') || name.includes('particular') || name.includes('details')) headerMap.description = idx;
        else if (name.includes('debit') || name.includes('withdrawal') || name.includes('dr')) headerMap.debit = idx;
        else if (name.includes('credit') || name.includes('deposit') || name.includes('cr')) headerMap.credit = idx;
        else if (name.includes('balance')) headerMap.balance = idx;
      });
    }

    table.rows.forEach(row => {
      // Skip empty rows
      if (!row || row.every(cell => !cell || String(cell).trim() === '')) return;

      // Extract by header mapping
      const get = (key) => headerMap[key] !== undefined && row[headerMap[key]] !== undefined ? String(row[headerMap[key]]).trim() : '';
      const date = get('date');
      const valuedate = get('valuedate');
      const description = get('description');
      const debitRaw = get('debit');
      const creditRaw = get('credit');
      const balanceRaw = get('balance');

      // Handle numeric values - they might already be numbers from Python
      let debit = null;
      let credit = null;
      let balance = null;

      if (debitRaw) {
        if (typeof debitRaw === 'number') {
          debit = debitRaw;
        } else if (typeof debitRaw === 'string' && debitRaw.match(/\d/)) {
          debit = parseFloat(debitRaw.replace(/,/g, ''));
        }
      }

      if (creditRaw) {
        if (typeof creditRaw === 'number') {
          credit = creditRaw;
        } else if (typeof creditRaw === 'string' && creditRaw.match(/\d/)) {
          credit = parseFloat(creditRaw.replace(/,/g, ''));
        }
      }

      if (balanceRaw) {
        if (typeof balanceRaw === 'number') {
          balance = balanceRaw;
        } else if (typeof balanceRaw === 'string' && balanceRaw.match(/\d/)) {
          balance = parseFloat(balanceRaw.replace(/,/g, ''));
        }
      }

      // Only one of debit/credit should be filled
      if (debit && credit) {
        // If both are filled, keep the non-zero or larger one, set the other to null
        if (Math.abs(debit) >= Math.abs(credit)) credit = null;
        else debit = null;
      }

      // At least one of date, description, debit, credit, or balance must be present
      if (date || description || debit !== null || credit !== null || balance !== null) {
        transactions.push({
          date,
          valuedate,
          description,
          withdrawal: debit,  // Map debit to withdrawal for compatibility
          deposit: credit,    // Map credit to deposit for compatibility
          balance
        });
      }
    });
  });

  return transactions;
};

// Handle manual extraction when automatic extraction fails
exports.processStatement = asyncHandler(async (req, res, next) => {
  if (!req.file && !req.body.filePath) {
    return res.status(400).json({ success: false, message: 'No file provided.' });
  }

  try {
    // Get file path from either the uploaded file or the provided path
    const filePath = req.file ? req.file.path : req.body.filePath;

    // Check if OCR is forced
    const forceOcr = req.body.forceOcr === 'true';

    if (forceOcr) {
      // Use Tesseract OCR for enhanced text extraction
      const result = await processPdfWithTesseract(filePath);

      if (result.success) {
        res.status(200).json({
          success: true,
          text: result.text,
          tables: result.tables,
          pageCount: result.pageCount,
          originalFilename: req.file ? req.file.originalname : path.basename(req.body.filePath)
        });
      } else {
        throw new Error(result.message);
      }
    } else {
      // Use standard PDF parsing
      const dataBuffer = fs.readFileSync(filePath);

      // Use custom render function for better table structure preservation
      const options = {
        pagerender: customRenderPage,
        max: 0 // Process all pages
      };

      const data = await pdfParse(dataBuffer, options);

      // Return the raw text for manual extraction in the frontend
      res.status(200).json({
        success: true,
        text: data.text,
        pageCount: data.numpages,
        originalFilename: req.file ? req.file.originalname : path.basename(req.body.filePath)
      });
    }

    // Clean up the file if it was uploaded in this request
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
  } catch (error) {
    console.error("Error processing PDF for manual extraction:", error);
    // Clean up file if it exists and was uploaded in this request
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    res.status(500).json({
      success: false,
      message: 'Error processing PDF for manual extraction',
      error: error.message
    });
  }
});

// Save manually extracted data
exports.saveManualExtraction = asyncHandler(async (req, res, next) => {
  const { data } = req.body;

  if (!data || !Array.isArray(data)) {
    return res.status(400).json({ success: false, message: 'Invalid data format.' });
  }

  try {
    // Here you could save the data to a database if needed
    // For now, just return success
    res.status(200).json({
      success: true,
      count: data.length,
      data: data
    });
  } catch (error) {
    console.error("Error saving manual extraction:", error);
    res.status(500).json({
      success: false,
      message: 'Error saving manual extraction',
      error: error.message
    });
  }
});

// Extract grid structures from PDF
exports.extractGrids = asyncHandler(async (req, res, next) => {
  if (!req.file && !req.body.filePath) {
    return res.status(400).json({ success: false, message: 'No file provided.' });
  }

  try {
    // Get file path from either the uploaded file or the provided path
    const filePath = req.file ? req.file.path : req.body.filePath;

    // Use the specialized grid extraction function
    const result = await extractGridsFromPdf(filePath);

    if (result.success) {
      res.status(200).json({
        success: true,
        tables: result.tables,
        pageCount: result.pageCount,
        originalFilename: req.file ? req.file.originalname : path.basename(req.body.filePath)
      });
    } else {
      throw new Error(result.message);
    }

    // Clean up the file if it was uploaded in this request
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
  } catch (error) {
    console.error("Error extracting grids from PDF:", error);
    // Clean up file if it exists and was uploaded in this request
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }
    res.status(500).json({
      success: false,
      message: 'Error extracting grids from PDF',
      error: error.message
    });
  }
});

// TODO: Implement download functionality (Excel/CSV) - Frontend seems to handle this
// TODO: Implement secure file deletion logic - Currently done after processing
// TODO: Refine transactionPattern for different bank formats
