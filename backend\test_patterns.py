#!/usr/bin/env python3
"""
Test script to verify amount extraction patterns work correctly
"""

import re

def safe_float_conversion(value):
    """Safely convert a value to float, handling various formats."""
    if value is None:
        return None
    
    if isinstance(value, (int, float)):
        return float(value)
    
    if isinstance(value, str):
        # Remove common formatting characters
        cleaned = value.strip().replace(',', '').replace('₹', '').replace('Rs.', '').replace('INR', '')
        
        # Handle negative values
        is_negative = cleaned.startswith('-') or cleaned.startswith('(')
        cleaned = cleaned.replace('-', '').replace('(', '').replace(')', '')
        
        try:
            result = float(cleaned)
            return -result if is_negative else result
        except ValueError:
            return None
    
    return None

def test_patterns():
    """Test the regex patterns with sample data"""
    
    # Sample descriptions from the actual bank statement
    test_cases = [
        "Purchase-************-130121 13:40:41-RELIANCE FRE -463237XXXXXX2723-258637",
        "ATM WITHDRAWAL -DPRH3973-NIPANIA INDORE -463237XXXXXX2723-********",
        "UPI~************~BHARATPE.********** @fbpe~DILIP -YBLd29201df2dab40d0a630e587088",
        "NEFT-0811OP1008156394-Generx Pharma -ICICI BANK LIMITED-ICIC0000709",
        "UPI~************~**********@okbizaxis ~Nidaan Pharm -YBLe918bed0d05441009d3d8ec4ac3",
        "Purchase with amount Rs. 1500.00",
        "Transfer INR 2500.50",
        "Payment ₹ 3000",
        "Transaction ending with 1234.56",
        "Payment for services 999",
        "UPI payment 15000"
    ]
    
    # Currency patterns
    currency_patterns = [
        r'Rs\.?\s*(\d+(?:,\d{3})*(?:\.\d{2})?)',  # Rs. 1500.00 or Rs. 1,000.00
        r'INR\s*(\d+(?:,\d{3})*(?:\.\d{2})?)',   # INR 1500.00 or INR 1,000.00
        r'₹\s*(\d+(?:,\d{3})*(?:\.\d{2})?)'      # ₹ 1500.00 or ₹ 1,000.00
    ]
    
    # End amount patterns
    end_amount_patterns = [
        r'\s(\d+(?:,\d{3})*\.\d{2})$',  # 1500.00 at end or 1,000.00 at end
        r'\s(\d{3,6})$',  # 100-999999 at end (reasonable range, avoid small numbers like IDs)
        r'\s(\d+(?:,\d{3})*\.\d{2})\s*$',  # 1500.00 near end or 1,000.00 near end
    ]
    
    print("Testing amount extraction patterns:")
    print("=" * 50)
    
    for i, description in enumerate(test_cases):
        print(f"\nTest {i+1}: {description}")
        
        # Test currency patterns
        amount_found = False
        amount = None
        
        for pattern in currency_patterns:
            matches = re.findall(pattern, description)
            if matches:
                amount_str = matches[-1].replace(',', '')  # Take the last match
                amount = safe_float_conversion(amount_str)
                if amount is not None and amount > 0 and amount < 10000000:  # Reasonable amount limit
                    print(f"  ✓ Found currency amount: {amount} using pattern: {pattern}")
                    amount_found = True
                    break
        
        # If no currency pattern found, look for amounts at the end of the description
        if not amount_found:
            for pattern in end_amount_patterns:
                matches = re.findall(pattern, description)
                if matches:
                    amount_str = matches[-1].replace(',', '')  # Take the last match
                    amount = safe_float_conversion(amount_str)
                    if amount is not None and amount > 0 and amount < 10000000:  # Reasonable amount limit
                        print(f"  ✓ Found end amount: {amount} using pattern: {pattern}")
                        amount_found = True
                        break
        
        if not amount_found:
            print("  ✗ No amount found")
        
        # Test classification
        if amount_found and amount is not None:
            desc_lower = description.lower()
            if any(keyword in desc_lower for keyword in ['debit', 'withdrawal', 'dr', 'paid', 'transfer']):
                print(f"  → Classified as DEBIT: {amount}")
            elif any(keyword in desc_lower for keyword in ['credit', 'deposit', 'cr', 'received', 'salary']):
                print(f"  → Classified as CREDIT: {amount}")
            else:
                # Default to credit for UPI transactions (common pattern)
                if 'upi' in desc_lower:
                    print(f"  → Classified as UPI CREDIT: {amount}")
                else:
                    print(f"  → Classified as DEFAULT DEBIT: {amount}")

if __name__ == "__main__":
    test_patterns()
