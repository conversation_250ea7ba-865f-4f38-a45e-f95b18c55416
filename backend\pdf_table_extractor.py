import sys
import json
import re
import traceback
import pandas as pd
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, stream=sys.stderr, format='%(asctime)s - %(levelname)s - %(message)s')

# Try to import Tabula
try:
    import tabula
    logging.info("Tabula imported successfully.")
except ImportError:
    tabula = None
    logging.warning("Tabula not found. Table extraction might be less effective.")

try:
    import pdfplumber
    logging.info("pdfplumber imported successfully.")
except ImportError:
    pdfplumber = None
    logging.error("pdfplumber not found. Cannot proceed with PDF analysis.")
    sys.exit(1) # Exit if pdfplumber is not available



# --- Helper function for robust float conversion ---
def safe_float_conversion(value_str):
    """Attempts to convert a string to a float, handling common financial formats."""
    if not isinstance(value_str, str):
        return None
    cleaned_str = value_str.strip().replace(',', '') # Remove commas
    # Handle parentheses for negative numbers, e.g., (100.00) -> -100.00
    if cleaned_str.startswith('(') and cleaned_str.endswith(')'):
        cleaned_str = '-' + cleaned_str[1:-1]
    try:
        return float(cleaned_str)
    except ValueError:
        return None # Return None if conversion fails

# --- Table Extraction using available libraries ---
def extract_tables_from_pdf(pdf_path, password=None):
    """Extracts tables from a PDF file using pdfplumber first, then tabula as fallback."""
    extracted_tables_data = [] # Store list of lists for each table

    # 1. Try pdfplumber first (primary method)
    # pdfplumber is often better for text-heavy PDFs or those with less defined lines
    try:
        logging.info(f"Trying pdfplumber on {pdf_path}")
        with pdfplumber.open(pdf_path, password=password) as pdf:
            for i, page in enumerate(pdf.pages, 1):
                plumber_tables = page.extract_tables()
                logging.info(f"pdfplumber page {i} found {len(plumber_tables) if plumber_tables else 0} tables")
                for t_idx, t in enumerate(plumber_tables):
                    if t and len(t) > 0:
                        extracted_tables_data.append({'data': t, 'page': i, 'flavor': 'pdfplumber'})
        if extracted_tables_data:
            logging.info(f"Extracted {len(extracted_tables_data)} tables with pdfplumber.")
            return extracted_tables_data
    except Exception as e:
        logging.error(f"pdfplumber extraction failed: {e}")
        # traceback.print_exc(file=sys.stderr)

    # 2. Try Tabula as fallback
    if tabula is not None:
        try:
            logging.info(f"Trying Tabula on {pdf_path}")
            tabula_tables = tabula.read_pdf(pdf_path, pages='all', multiple_tables=True, pandas_options={'dtype': str})
            logging.info(f"Tabula found {len(tabula_tables) if tabula_tables else 0} tables")
            for i, df in enumerate(tabula_tables):
                if not df.empty and df.shape[0] > 0:
                    extracted_tables_data.append({'data': df.values.tolist(), 'page': i+1, 'flavor': 'tabula'})
            if extracted_tables_data:
                logging.info(f"Extracted {len(extracted_tables_data)} tables with Tabula.")
                return extracted_tables_data
        except Exception as e:
            logging.error(f"Tabula extraction failed: {e}")
            # traceback.print_exc(file=sys.stderr)

    logging.warning("All primary extraction methods failed. Falling back to raw text lines.")
    # 4. Fallback: extract raw text lines as a single-column table
    try:
        with pdfplumber.open(pdf_path, password=password) as pdf:
            all_lines = []
            for page_num, page in enumerate(pdf.pages, 1):
                text = page.extract_text()
                if text:
                    lines = [line.strip() for line in text.split('\n') if line.strip()]
                    logging.info(f"pdfplumber page {page_num} extracted {len(lines)} lines of text")
                    all_lines.extend(lines)
            if all_lines:
                # Represent raw text as a single column table
                extracted_tables_data.append({'data': [[line] for line in all_lines], 'page': 1, 'flavor': 'raw-text'})
                logging.info(f"Extracted {len(all_lines)} raw text lines as fallback.")
                return extracted_tables_data
    except Exception as e:
        logging.error(f"Raw text extraction failed: {e}")
        # traceback.print_exc(file=sys.stderr)


    logging.error("All extraction methods failed.")
    return None


# --- Helper function to split concatenated rows ---
def split_concatenated_row(row_str, dates_found, headers):
    """
    Attempts to split a concatenated row containing multiple transactions
    based on date patterns found in the first column.
    """
    split_rows = []

    if len(row_str) < 2:
        return [row_str]  # Not enough data to split

    date_column = row_str[0]
    desc_column = row_str[1] if len(row_str) > 1 else ''

    # Try to split based on date positions
    date_pattern = r'\d{2}-\w{3}-\d{4}'

    # Find all date positions in the first column
    date_matches = list(re.finditer(date_pattern, date_column))

    if len(date_matches) <= 1:
        return [row_str]  # No splitting needed

    # Split the description column based on transaction patterns
    # Look for UPI patterns, NEFT patterns, etc. to help split descriptions
    transaction_patterns = [
        r'UPI~\d+~[^~]+~[^~]+',
        r'NEFT[^A-Z]*[A-Z]',
        r'IMPS[^A-Z]*[A-Z]',
        r'Purchase-\d+',
        r'ATM WITHDRAWAL'
    ]

    # Try to find transaction boundaries in description using UPI patterns
    upi_pattern = r'UPI~\d+~[^~]+~[^~]+[^U]*(?=UPI~|\s*$)'
    upi_matches = list(re.finditer(upi_pattern, desc_column))

    if len(upi_matches) > 0:
        # Use UPI transaction boundaries
        for i, upi_match in enumerate(upi_matches):
            if i < len(date_matches):
                date = date_matches[i].group()
                description = upi_match.group().strip()

                # Try to extract numeric values from the description or remaining columns
                debit_value = ''
                credit_value = ''
                balance_value = ''

                # Look for numeric patterns in the description or other columns
                if len(row_str) > 2:
                    # Check if there are numeric values in subsequent columns
                    for col_idx in range(2, len(row_str)):
                        if row_str[col_idx] and row_str[col_idx].strip():
                            numeric_val = safe_float_conversion(row_str[col_idx])
                            if numeric_val is not None:
                                if col_idx == 2:  # Debit column
                                    debit_value = numeric_val
                                elif col_idx == 3:  # Credit column
                                    credit_value = numeric_val
                                elif col_idx == 4:  # Balance column
                                    balance_value = numeric_val

                # Create a new row with the split data including numeric values
                new_row = [date, description, debit_value, credit_value, balance_value]
                # Ensure we have the right number of columns
                while len(new_row) < len(headers):
                    new_row.append('')
                new_row = new_row[:len(headers)]
                split_rows.append(new_row)
    else:
        # Try other transaction patterns
        desc_splits = []
        for pattern in transaction_patterns:
            matches = list(re.finditer(pattern, desc_column))
            for match in matches:
                desc_splits.append(match.start())

        desc_splits = sorted(set(desc_splits))

        # If we have transaction patterns, use them
        if len(desc_splits) > 0:
            for i, date_match in enumerate(date_matches):
                date = date_match.group()

                if i < len(desc_splits):
                    desc_start = desc_splits[i]
                    desc_end = desc_splits[i + 1] if i + 1 < len(desc_splits) else len(desc_column)
                    description = desc_column[desc_start:desc_end].strip()
                else:
                    description = ''

                # Only add rows with meaningful descriptions
                if description:
                    # Try to extract numeric values from remaining columns
                    debit_value = ''
                    credit_value = ''
                    balance_value = ''

                    if len(row_str) > 2:
                        for col_idx in range(2, len(row_str)):
                            if row_str[col_idx] and row_str[col_idx].strip():
                                numeric_val = safe_float_conversion(row_str[col_idx])
                                if numeric_val is not None:
                                    if col_idx == 2:  # Debit column
                                        debit_value = numeric_val
                                    elif col_idx == 3:  # Credit column
                                        credit_value = numeric_val
                                    elif col_idx == 4:  # Balance column
                                        balance_value = numeric_val

                    new_row = [date, description, debit_value, credit_value, balance_value]
                    while len(new_row) < len(headers):
                        new_row.append('')
                    new_row = new_row[:len(headers)]
                    split_rows.append(new_row)
        else:
            # Fallback: just use the first date with the full description
            if date_matches:
                date = date_matches[0].group()

                # Try to extract numeric values from remaining columns
                debit_value = ''
                credit_value = ''
                balance_value = ''

                if len(row_str) > 2:
                    for col_idx in range(2, len(row_str)):
                        if row_str[col_idx] and row_str[col_idx].strip():
                            numeric_val = safe_float_conversion(row_str[col_idx])
                            if numeric_val is not None:
                                if col_idx == 2:  # Debit column
                                    debit_value = numeric_val
                                elif col_idx == 3:  # Credit column
                                    credit_value = numeric_val
                                elif col_idx == 4:  # Balance column
                                    balance_value = numeric_val

                new_row = [date, desc_column.strip(), debit_value, credit_value, balance_value]
                while len(new_row) < len(headers):
                    new_row.append('')
                new_row = new_row[:len(headers)]
                split_rows.append(new_row)

    return split_rows if split_rows else [row_str]


# --- Table Unification and Cleaning (Robust Header and Row Parsing) ---
def unify_tables(extracted_tables_data):
    """
    Unifies tables extracted from different pages/methods into a single table.
    Identifies the header row and extracts data rows, handling multi-page tables.
    Processes rows based on identified header columns.
    """
    if not extracted_tables_data:
        logging.warning("No tables data provided for unification.")
        return None, None

    # Define expected header keywords for reference (used in header detection logic below)
    # ["date", "transaction", "trans"],  # Date-related headers
    # ["description", "details", "particulars", "narration"],  # Description headers
    # ["debit", "withdrawal", "dr"],  # Debit headers
    # ["credit", "deposit", "cr"],  # Credit headers
    # ["balance", "bal"]  # Balance headers
    identified_headers = None
    header_row_index = -1
    all_data_rows = []
    header_identified_page = -1

    # Sort tables by page number to process them in chronological order
    extracted_tables_data.sort(key=lambda x: x['page'])

    for table_info in extracted_tables_data:
        table_data = table_info['data']
        page_num = table_info['page']
        flavor = table_info['flavor']
        logging.info(f"Processing table from page {page_num}, flavor: {flavor}")
        logging.debug(f"Raw table data from page {page_num}, flavor {flavor}: {table_data}")

        # Attempt to find the header row if not already identified
        if identified_headers is None:
            for row_idx, row in enumerate(table_data):
                # Convert row cells to strings and clean them
                row_str = [str(cell).strip().replace('\n', ' ') if cell is not None else '' for cell in row]
                logging.info(f"Checking row {row_idx} on page {page_num} for header: {row_str}")

                # Check if this row contains header-like keywords (more flexible matching)
                # Look for transaction table headers (Debit and Credit are required)
                has_debit = any(any(re.search(keyword, cell, re.IGNORECASE) for keyword in ["debit", "withdrawal", "dr"]) for cell in row_str)
                has_credit = any(any(re.search(keyword, cell, re.IGNORECASE) for keyword in ["credit", "deposit", "cr"]) for cell in row_str)
                has_date = any(any(re.search(keyword, cell, re.IGNORECASE) for keyword in ["date", "transaction", "trans"]) for cell in row_str)
                has_description = any(any(re.search(keyword, cell, re.IGNORECASE) for keyword in ["description", "details", "particulars", "narration"]) for cell in row_str)

                # Must have Debit, Credit, and at least one date/description column for transaction table
                if len(row_str) >= 3 and has_debit and has_credit and (has_date or has_description):
                    identified_headers = row_str
                    header_row_index = row_idx
                    header_identified_page = page_num
                    logging.info(f"Identified header row on page {page_num}, row {row_idx}: {identified_headers}")

                    # Clean up headers to standardize names, but keep original structure
                    cleaned_headers = []
                    for header_cell in identified_headers:
                        header_lower = header_cell.lower()
                        if "value" in header_lower and "date" in header_lower:
                            cleaned_headers.append("Value Date")
                        elif any(keyword in header_lower for keyword in ["date", "transaction", "trans"]) and "value" not in header_lower:
                            cleaned_headers.append("Transaction Date")
                        elif any(keyword in header_lower for keyword in ["description", "details", "particulars", "narration", "transaction"]) and "date" not in header_lower:
                            cleaned_headers.append("Description")
                        elif any(keyword in header_lower for keyword in ["debit", "withdrawal", "dr"]):
                            cleaned_headers.append("Debit")
                        elif any(keyword in header_lower for keyword in ["credit", "deposit", "cr"]):
                            cleaned_headers.append("Credit")
                        elif any(keyword in header_lower for keyword in ["balance", "bal"]):
                            cleaned_headers.append("Balance")
                        else:
                            cleaned_headers.append(header_cell) # Keep as is if not a standard header

                    # Ensure we have the required columns: Debit, Credit (Balance is optional)
                    required_columns = ["Debit", "Credit"]
                    missing_columns = [col for col in required_columns if col not in cleaned_headers]
                    if missing_columns:
                        logging.warning(f"Header row missing required columns: {missing_columns}. Skipping this header.")
                        continue  # Skip this header and keep looking

                    # Add Balance column if missing (as requested by user)
                    if "Balance" not in cleaned_headers:
                        cleaned_headers.append("Balance")
                        logging.info("Added Balance column to headers as requested")

                    identified_headers = cleaned_headers
                    logging.info(f"Cleaned headers: {identified_headers}")
                    break # Found header, stop searching in this table

            if identified_headers is None:
                 logging.warning(f"Could not find expected header keywords in table on page {page_num}. Skipping this table for header identification.")
                 # Log the first few rows for debugging
                 for i, row in enumerate(table_data[:3]):
                     row_str = [str(cell).strip().replace('\n', ' ') if cell is not None else '' for cell in row]
                     logging.info(f"Sample row {i} on page {page_num}: {row_str}")
                 continue # Continue to the next table to find the header

        # Process data rows from the current table
        # Start processing from the row after the header if header was found on this page, otherwise start from the beginning
        start_row_for_data = header_row_index + 1 if page_num == header_identified_page else 0

        for row_idx in range(start_row_for_data, len(table_data)):
            row = table_data[row_idx]
            # Convert row cells to strings and clean them
            row_str = [str(cell).strip().replace('\n', ' ') if cell is not None else '' for cell in row]
            logging.debug(f"Processing data row {row_idx} on page {page_num}: {row_str}")
            logging.debug(f"Row length: {len(row_str)}, Original row: {row}")

            # Basic filtering for rows that might be footers or summaries within the table data
            if any('closing balance' in cell.lower() for cell in row_str):
                 logging.info(f"Detected 'Closing Balance' row on page {page_num}, row {row_idx}. Ending table processing for this table.")
                 break # Stop processing rows for this table if closing balance or similar found

            # Skip header rows that might appear in data
            if any('value date' in cell.lower() or 'details of transaction' in cell.lower() for cell in row_str):
                logging.debug(f"Skipping header row in data on page {page_num}, row {row_idx}: {row_str}")
                continue

            # Check if this row contains concatenated data (multiple dates in first column)
            if len(row_str) > 0 and row_str[0]:
                # Look for multiple date patterns in the first column
                date_pattern = r'\d{2}-\w{3}-\d{4}'
                dates_found = re.findall(date_pattern, row_str[0])

                if len(dates_found) > 1:
                    # This row contains multiple transactions concatenated
                    logging.info(f"Found concatenated row with {len(dates_found)} dates on page {page_num}, row {row_idx}")

                    # Try to split the concatenated data
                    split_rows = split_concatenated_row(row_str, dates_found, identified_headers)
                    for split_row in split_rows:
                        if any(cell.strip() for cell in split_row):
                            all_data_rows.append(split_row)
                            logging.debug(f"Added split row: {split_row}")
                    continue

            # Assume this is a data row and process it normally
            # Ensure the processed row has the same number of columns as the header
            processed_row = (row_str + [''] * len(identified_headers))[:len(identified_headers)]

            # Enhanced check to skip rows that are empty or have only dates without meaningful content
            has_meaningful_content = (
                len(processed_row) > 1 and
                processed_row[1] and  # Description column has content
                processed_row[1].strip() and
                len(processed_row[1].strip()) > 5  # Description has more than just a few characters
            )

            if has_meaningful_content:
                # Try to extract numeric values from the description if they're embedded there
                # This is common in bank statements where amounts might be in the description
                if len(identified_headers) >= 5:  # We have Debit, Credit, Balance columns in headers
                    description = processed_row[1]
                    logging.debug(f"Processing description for amounts: '{description}'")
                    logging.debug(f"Headers length: {len(identified_headers)}, Headers: {identified_headers}")
                    logging.debug(f"Processed row length: {len(processed_row)}, Row: {processed_row}")

                    # Look for numeric patterns in the description
                    # Strategy: Look for amounts at the end of descriptions or after specific keywords
                    # Avoid UPI transaction IDs which are very long numbers

                    # First try to find amounts with currency indicators
                    currency_patterns = [
                        r'Rs\.?\s*(\d+(?:,\d{3})*(?:\.\d{2})?)',  # Rs. 1500.00 or Rs. 1,000.00
                        r'INR\s*(\d+(?:,\d{3})*(?:\.\d{2})?)',   # INR 1500.00 or INR 1,000.00
                        r'₹\s*(\d+(?:,\d{3})*(?:\.\d{2})?)'      # ₹ 1500.00 or ₹ 1,000.00
                    ]

                    amount_found = False
                    amount = None
                    for pattern in currency_patterns:
                        matches = re.findall(pattern, description)
                        if matches:
                            amount_str = matches[-1].replace(',', '')  # Take the last match
                            amount = safe_float_conversion(amount_str)
                            if amount is not None and amount > 0 and amount < ********:  # Reasonable amount limit
                                logging.debug(f"Found currency amount: {amount} using pattern: {pattern}")
                                amount_found = True
                                break

                    # If no currency pattern found, look for amounts at the end of the description
                    if not amount_found:
                        # Look for amounts at the end of the description (common in bank statements)
                        end_amount_patterns = [
                            r'\s(\d+(?:,\d{3})*\.\d{2})$',  # 1500.00 at end or 1,000.00 at end
                            r'\s(\d{3,6})$',  # 100-999999 at end (reasonable range, avoid small numbers like IDs)
                            r'\s(\d+(?:,\d{3})*\.\d{2})\s*$',  # 1500.00 near end or 1,000.00 near end
                        ]

                        for pattern in end_amount_patterns:
                            matches = re.findall(pattern, description)
                            if matches:
                                amount_str = matches[-1].replace(',', '')  # Take the last match
                                amount = safe_float_conversion(amount_str)
                                if amount is not None and amount > 0 and amount < ********:  # Reasonable amount limit
                                    logging.debug(f"Found end amount: {amount} using pattern: {pattern}")
                                    amount_found = True
                                    break

                    if amount_found and amount is not None:
                        # Determine if it's debit or credit based on keywords
                        desc_lower = description.lower()
                        if any(keyword in desc_lower for keyword in ['debit', 'withdrawal', 'dr', 'paid', 'transfer']):
                            processed_row[2] = amount  # Debit column
                            logging.debug(f"Classified as DEBIT: {amount}")
                        elif any(keyword in desc_lower for keyword in ['credit', 'deposit', 'cr', 'received', 'salary']):
                            processed_row[3] = amount  # Credit column
                            logging.debug(f"Classified as CREDIT: {amount}")
                        else:
                            # Default to credit for UPI transactions (common pattern)
                            if 'upi' in desc_lower:
                                processed_row[3] = amount  # Credit column
                                logging.debug(f"Classified as UPI CREDIT: {amount}")
                            else:
                                processed_row[2] = amount  # Debit column
                                logging.debug(f"Classified as DEFAULT DEBIT: {amount}")
                    else:
                        logging.debug(f"No amount found in description: '{description}'")

                all_data_rows.append(processed_row)
                logging.debug(f"Added data row on page {page_num}, row {row_idx}: {processed_row}")
            else:
                logging.debug(f"Skipping empty or low-content row on page {page_num}, row {row_idx}: {row_str}")


    # Post-processing: Attempt to parse numeric values in Debit/Credit/Balance columns
    # Only convert if the value is a string and not already a number
    if identified_headers:
        debit_col_idx = identified_headers.index("Debit") if "Debit" in identified_headers else -1
        credit_col_idx = identified_headers.index("Credit") if "Credit" in identified_headers else -1
        balance_col_idx = identified_headers.index("Balance") if "Balance" in identified_headers else -1

        processed_data_rows = []
        for row in all_data_rows:
            processed_row = list(row) # Create a mutable copy
            # Only convert string values to numbers, preserve already-extracted numeric values
            if debit_col_idx != -1 and debit_col_idx < len(processed_row):
                if isinstance(row[debit_col_idx], str) and row[debit_col_idx].strip():
                    processed_row[debit_col_idx] = safe_float_conversion(row[debit_col_idx])
                # Keep existing numeric values as-is
            if credit_col_idx != -1 and credit_col_idx < len(processed_row):
                if isinstance(row[credit_col_idx], str) and row[credit_col_idx].strip():
                    processed_row[credit_col_idx] = safe_float_conversion(row[credit_col_idx])
                # Keep existing numeric values as-is
            if balance_col_idx != -1 and balance_col_idx < len(processed_row):
                if isinstance(row[balance_col_idx], str) and row[balance_col_idx].strip():
                    processed_row[balance_col_idx] = safe_float_conversion(row[balance_col_idx])
                # Keep existing numeric values as-is
            processed_data_rows.append(processed_row)
    else:
        processed_data_rows = all_data_rows # No headers, no specific column processing

    # If no headers were identified, try to use the first row of the first table as headers
    if identified_headers is None and extracted_tables_data:
        logging.warning("No headers identified using keyword matching. Attempting to use first row as headers.")
        first_table = extracted_tables_data[0]
        if first_table['data'] and len(first_table['data']) > 0:
            first_row = first_table['data'][0]
            identified_headers = [str(cell).strip().replace('\n', ' ') if cell is not None else f'Column_{i+1}'
                                for i, cell in enumerate(first_row)]
            logging.info(f"Using first row as headers: {identified_headers}")

            # Collect all data rows from all tables (excluding the first row of the first table)
            all_data_rows = []
            for table_info in extracted_tables_data:
                table_data = table_info['data']
                start_idx = 1 if table_info == first_table else 0  # Skip first row only for first table
                for row_idx in range(start_idx, len(table_data)):
                    row = table_data[row_idx]
                    row_str = [str(cell).strip().replace('\n', ' ') if cell is not None else '' for cell in row]
                    # Ensure the processed row has the same number of columns as the header
                    processed_row = (row_str + [''] * len(identified_headers))[:len(identified_headers)]
                    if any(cell.strip() for cell in processed_row):
                        all_data_rows.append(processed_row)

            processed_data_rows = all_data_rows

    return identified_headers, processed_data_rows


# --- Main PDF Processing Entrypoint ---
def process_pdf_action(action, pdf_path, password=None):
    if action == "convert":
        logging.info(f"Processing PDF for conversion: {pdf_path}")
        extracted_tables_data = extract_tables_from_pdf(pdf_path, password=password)

        if extracted_tables_data is None or not extracted_tables_data:
            output_data = {"error": "Failed to extract any tables or text from PDF"}
            print(json.dumps(output_data), file=sys.stdout)
            sys.exit(1)

        headers, rows = unify_tables(extracted_tables_data)

        if headers is None:
             output_data = {"error": "Could not identify table headers in the extracted data."}
             print(json.dumps(output_data), file=sys.stdout)
             sys.exit(1)


        output_data = {
            "status": "Bank statement data extracted and unified",
            "num_extracted_tables_raw": len(extracted_tables_data),
            "tables_data": [{
                "headers": headers,
                "rows": rows,
                "page": "unified",
                "flavor": "unified"
            }]
        }
        logging.info("Successfully processed PDF for conversion.")
        print(json.dumps(output_data, indent=2))

    elif action == "inspect":
        logging.info(f"Processing PDF for inspection: {pdf_path}")
        # For inspection, return raw extracted tables before unification
        extracted_tables_data = extract_tables_from_pdf(pdf_path, password=password)

        if extracted_tables_data is None or not extracted_tables_data:
            output_data = {"error": "Failed to extract any tables or text for inspection"}
            print(json.dumps(output_data), file=sys.stdout)
            sys.exit(1)

        # Format for inspection output
        inspection_output_tables = []
        for table_info in extracted_tables_data:
            inspection_output_tables.append({
                'page': table_info.get('page', 'N/A'),
                'flavor': table_info.get('flavor', 'N/A'),
                'data': table_info['data']
            })


        output_data = {
            "status": "Raw tables extracted for inspection",
            "num_tables": len(inspection_output_tables),
            "tables_data": inspection_output_tables
        }
        logging.info("Successfully processed PDF for inspection.")
        print(json.dumps(output_data, indent=2))

    else:
        logging.error(f"Unknown action: {action}. Supported actions: convert, inspect")
        print(json.dumps({"error": f"Unknown action: {action}"}), file=sys.stdout)
        sys.exit(1)

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("Usage: python pdf_table_extractor.py <action> <pdf_path> [password]", file=sys.stderr)
        sys.exit(1)
    action = sys.argv[1]
    pdf_path = sys.argv[2]
    password = sys.argv[3] if len(sys.argv) > 3 else None

    try:
        process_pdf_action(action, pdf_path, password=password)
    except Exception as e:
        logging.error(f"An unexpected error occurred during processing: {e}")
        traceback.print_exc(file=sys.stderr)
        print(json.dumps({"error": f"An unexpected error occurred: {e}"}), file=sys.stdout)
        sys.exit(1)
