import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import axios from 'axios'; // Import axios

function ConversionResultsPage() {
  const location = useLocation();
  const [extractedData, setExtractedData] = useState(null); // Use extractedData for the new structure
  const [originalFilename, setOriginalFilename] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const uuid = params.get('uuids');
    const filename = params.get('filename');

    if (uuid) {
      setOriginalFilename(filename || 'Uploaded File');
      // Fetch extracted data from the backend using the UUID
      axios.get(`/api/conversion-results/${uuid}`)
        .then(res => {
          console.log("Frontend received extracted data:", res.data); // Add logging
          setExtractedData(res.data); // Set the new data structure
          setIsLoading(false);
        })
        .catch(err => {
          console.error('Error fetching extracted data:', err);
          setError('Could not load extracted data.');
          setIsLoading(false);
        });
    } else {
      setError('No conversion ID provided.');
      setIsLoading(false);
    }
  }, [location.search]); // Re-run effect if query parameters change

  if (isLoading) {
    return (
      <div className="container">
        <h2>Conversion Results</h2>
        <p>Loading extracted data...</p>
        {/* Add a loading spinner here */}
      </div>
    );
  }

  if (error) {
    return (
      <div className="container">
        <h2>Conversion Results</h2>
        <p className="error">{error}</p>
      </div>
    );
  }

  // Handle both old unified_table format and new tables_data format
  const tablesData = extractedData?.tables_data ||
                    (extractedData?.unified_table ? [extractedData.unified_table] : null);

  if (!extractedData || !tablesData || tablesData.length === 0) {
    return (
      <div className="container">
        <h2>Conversion Results</h2>
        <p>No table data available.</p>
        {extractedData && extractedData.status && <p>Status: {extractedData.status}</p>}
        {extractedData && extractedData.error && <p>Backend Error: {extractedData.error}</p>}
      </div>
    );
  }

  // Render all tables from tables_data using backend-provided headers and rows
  return (
    <div className="container">
      <h2>Conversion Results</h2>
      <p>Processed {tablesData.length} tables from: {originalFilename}</p>
      <div className="table-container">
        {tablesData.map((table, tableIdx) => (
          <div key={tableIdx} style={{ marginBottom: '2rem' }}>
            <h4>Table {tableIdx + 1}</h4>
            <table className="results-table">
              <thead>
                <tr>
                  {table.headers && table.headers.map((header, idx) => (
                    <th key={idx}>{header}</th>
                  ))}
                </tr>
              </thead>
              <tbody>
                {table.rows && table.rows.map((row, rowIdx) => (
                  <tr key={rowIdx}>
                    {row.map((cell, cellIdx) => (
                      <td key={cellIdx}>{cell !== null ? String(cell) : ''}</td>
                    ))}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ))}
      </div>
      <div className="download-options">
         <p>Download options will be available once the conversion logic is fully implemented.</p>
      </div>
    </div>
  );
}

export default ConversionResultsPage;
