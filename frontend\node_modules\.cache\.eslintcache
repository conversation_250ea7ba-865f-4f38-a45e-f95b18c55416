[{"F:\\converterpdf\\frontend\\src\\index.js": "1", "F:\\converterpdf\\frontend\\src\\App.js": "2", "F:\\converterpdf\\frontend\\src\\reportWebVitals.js": "3", "F:\\converterpdf\\frontend\\src\\pages\\HomePage.js": "4", "F:\\converterpdf\\frontend\\src\\pages\\LoginPage.js": "5", "F:\\converterpdf\\frontend\\src\\pages\\RegisterPage.js": "6", "F:\\converterpdf\\frontend\\src\\pages\\ResultsPage.js": "7", "F:\\converterpdf\\frontend\\src\\components\\Header.js": "8", "F:\\converterpdf\\frontend\\src\\components\\ManualExtraction.js": "9", "F:\\converterpdf\\frontend\\src\\pages\\InspectPage.js": "10", "F:\\converterpdf\\frontend\\src\\pdfWorker.js": "11", "F:\\converterpdf\\frontend\\src\\components\\PDFViewer.js": "12", "F:\\converterpdf\\frontend\\src\\components\\SimplePDFViewer.js": "13", "F:\\converterpdf\\frontend\\src\\components\\GlobalPDFViewer.js": "14", "F:\\converterpdf\\frontend\\src\\pages\\InspectRedirect.js": "15", "F:\\converterpdf\\frontend\\src\\components\\IframePDFViewer.js": "16", "F:\\converterpdf\\frontend\\src\\components\\DirectPDFViewer.js": "17", "F:\\converterpdf\\frontend\\src\\components\\FallbackPDFViewer.js": "18", "F:\\converterpdf\\frontend\\src\\components\\PDFScreenshotCapture.js": "19", "F:\\converterpdf\\frontend\\src\\utils\\captureUtils.js": "20", "F:\\converterpdf\\frontend\\src\\components\\SimplePDFCapture.js": "21", "F:\\converterpdf\\frontend\\src\\config\\pdfjs-config.js": "22", "F:\\converterpdf\\frontend\\src\\pdfPatch.js": "23", "F:\\converterpdf\\frontend\\src\\pages\\NewInspectPage.js": "24", "F:\\converterpdf\\frontend\\src\\pdfjs\\pdf.js": "25", "F:\\converterpdf\\frontend\\src\\pages\\SimpleInspectPage.js": "26", "F:\\converterpdf\\frontend\\src\\utils\\pdfTextUtils.js": "27", "F:\\converterpdf\\frontend\\src\\pages\\ConversionResultsPage.js": "28"}, {"size": 535, "mtime": 1744746615671, "results": "29", "hashOfConfig": "30"}, {"size": 2112, "mtime": 1747903361239, "results": "31", "hashOfConfig": "30"}, {"size": 362, "mtime": 1744746616368, "results": "32", "hashOfConfig": "30"}, {"size": 15623, "mtime": 1748287328571, "results": "33", "hashOfConfig": "30"}, {"size": 2476, "mtime": 1744747087567, "results": "34", "hashOfConfig": "30"}, {"size": 2172, "mtime": 1744747502485, "results": "35", "hashOfConfig": "30"}, {"size": 514, "mtime": 1744747526816, "results": "36", "hashOfConfig": "30"}, {"size": 2127, "mtime": 1746449903442, "results": "37", "hashOfConfig": "30"}, {"size": 7259, "mtime": 1745006921887, "results": "38", "hashOfConfig": "30"}, {"size": 10106, "mtime": 1745258036771, "results": "39", "hashOfConfig": "30"}, {"size": 186, "mtime": 1745079069423, "results": "40", "hashOfConfig": "30"}, {"size": 3892, "mtime": 1745345420200, "results": "41", "hashOfConfig": "30"}, {"size": 3956, "mtime": 1745090158046, "results": "42", "hashOfConfig": "30"}, {"size": 17255, "mtime": 1745170901708, "results": "43", "hashOfConfig": "30"}, {"size": 1285, "mtime": 1745090580938, "results": "44", "hashOfConfig": "30"}, {"size": 1591, "mtime": 1745171646222, "results": "45", "hashOfConfig": "30"}, {"size": 1313, "mtime": 1745171694951, "results": "46", "hashOfConfig": "30"}, {"size": 2696, "mtime": 1745172057246, "results": "47", "hashOfConfig": "30"}, {"size": 3620, "mtime": 1745176429226, "results": "48", "hashOfConfig": "30"}, {"size": 1777, "mtime": 1745174253244, "results": "49", "hashOfConfig": "30"}, {"size": 1919, "mtime": 1745176684820, "results": "50", "hashOfConfig": "30"}, {"size": 594, "mtime": 1746984513232, "results": "51", "hashOfConfig": "30"}, {"size": 551, "mtime": 1747902474074, "results": "52", "hashOfConfig": "30"}, {"size": 21692, "mtime": 1745436259151, "results": "53", "hashOfConfig": "30"}, {"size": 687, "mtime": 1746906232432, "results": "54", "hashOfConfig": "30"}, {"size": 38347, "mtime": 1748450596811, "results": "55", "hashOfConfig": "30"}, {"size": 2306, "mtime": 1746817513209, "results": "56", "hashOfConfig": "30"}, {"size": 3800, "mtime": 1748682559853, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "v4ugf6", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "F:\\converterpdf\\frontend\\src\\index.js", [], [], "F:\\converterpdf\\frontend\\src\\App.js", [], [], "F:\\converterpdf\\frontend\\src\\reportWebVitals.js", [], [], "F:\\converterpdf\\frontend\\src\\pages\\HomePage.js", ["142", "143", "144", "145", "146", "147"], ["148"], "F:\\converterpdf\\frontend\\src\\pages\\LoginPage.js", [], [], "F:\\converterpdf\\frontend\\src\\pages\\RegisterPage.js", ["149"], [], "F:\\converterpdf\\frontend\\src\\pages\\ResultsPage.js", [], [], "F:\\converterpdf\\frontend\\src\\components\\Header.js", [], [], "F:\\converterpdf\\frontend\\src\\components\\ManualExtraction.js", ["150", "151", "152"], [], "F:\\converterpdf\\frontend\\src\\pages\\InspectPage.js", [], ["153", "154"], "F:\\converterpdf\\frontend\\src\\pdfWorker.js", [], [], "F:\\converterpdf\\frontend\\src\\components\\PDFViewer.js", [], [], "F:\\converterpdf\\frontend\\src\\components\\SimplePDFViewer.js", ["155"], [], "F:\\converterpdf\\frontend\\src\\components\\GlobalPDFViewer.js", ["156", "157", "158", "159"], [], "F:\\converterpdf\\frontend\\src\\pages\\InspectRedirect.js", [], [], "F:\\converterpdf\\frontend\\src\\components\\IframePDFViewer.js", [], [], "F:\\converterpdf\\frontend\\src\\components\\DirectPDFViewer.js", [], [], "F:\\converterpdf\\frontend\\src\\components\\FallbackPDFViewer.js", [], [], "F:\\converterpdf\\frontend\\src\\components\\PDFScreenshotCapture.js", ["160", "161"], [], "F:\\converterpdf\\frontend\\src\\utils\\captureUtils.js", [], [], "F:\\converterpdf\\frontend\\src\\components\\SimplePDFCapture.js", [], [], "F:\\converterpdf\\frontend\\src\\config\\pdfjs-config.js", [], [], "F:\\converterpdf\\frontend\\src\\pdfPatch.js", [], [], "F:\\converterpdf\\frontend\\src\\pages\\NewInspectPage.js", ["162", "163", "164", "165", "166", "167"], [], "F:\\converterpdf\\frontend\\src\\pdfjs\\pdf.js", ["168"], [], "F:\\converterpdf\\frontend\\src\\pages\\SimpleInspectPage.js", ["169", "170"], [], "F:\\converterpdf\\frontend\\src\\utils\\pdfTextUtils.js", [], [], "F:\\converterpdf\\frontend\\src\\pages\\ConversionResultsPage.js", [], [], {"ruleId": "171", "severity": 1, "message": "172", "line": 7, "column": 7, "nodeType": "173", "messageId": "174", "endLine": 7, "endColumn": 22}, {"ruleId": "171", "severity": 1, "message": "175", "line": 32, "column": 7, "nodeType": "173", "messageId": "174", "endLine": 32, "endColumn": 20}, {"ruleId": "171", "severity": 1, "message": "176", "line": 60, "column": 10, "nodeType": "173", "messageId": "174", "endLine": 60, "endColumn": 20}, {"ruleId": "171", "severity": 1, "message": "177", "line": 61, "column": 10, "nodeType": "173", "messageId": "174", "endLine": 61, "endColumn": 18}, {"ruleId": "171", "severity": 1, "message": "178", "line": 62, "column": 10, "nodeType": "173", "messageId": "174", "endLine": 62, "endColumn": 25}, {"ruleId": "171", "severity": 1, "message": "179", "line": 182, "column": 9, "nodeType": "173", "messageId": "174", "endLine": 182, "endColumn": 22}, {"ruleId": "171", "severity": 1, "message": "180", "line": 90, "column": 9, "nodeType": "173", "messageId": "174", "endLine": 90, "endColumn": 21, "suppressions": "181"}, {"ruleId": "171", "severity": 1, "message": "182", "line": 18, "column": 13, "nodeType": "173", "messageId": "174", "endLine": 18, "endColumn": 16}, {"ruleId": "171", "severity": 1, "message": "183", "line": 9, "column": 10, "nodeType": "173", "messageId": "174", "endLine": 9, "endColumn": 23}, {"ruleId": "171", "severity": 1, "message": "184", "line": 9, "column": 25, "nodeType": "173", "messageId": "174", "endLine": 9, "endColumn": 41}, {"ruleId": "185", "severity": 1, "message": "186", "line": 17, "column": 6, "nodeType": "187", "endLine": 17, "endColumn": 8, "suggestions": "188"}, {"ruleId": "171", "severity": 1, "message": "189", "line": 10, "column": 30, "nodeType": "173", "messageId": "174", "endLine": 10, "endColumn": 35, "suppressions": "190"}, {"ruleId": "171", "severity": 1, "message": "191", "line": 10, "column": 60, "nodeType": "173", "messageId": "174", "endLine": 10, "endColumn": 68, "suppressions": "192"}, {"ruleId": "171", "severity": 1, "message": "193", "line": 22, "column": 10, "nodeType": "173", "messageId": "174", "endLine": 22, "endColumn": 18}, {"ruleId": "171", "severity": 1, "message": "194", "line": 182, "column": 13, "nodeType": "173", "messageId": "174", "endLine": 182, "endColumn": 26}, {"ruleId": "185", "severity": 1, "message": "195", "line": 324, "column": 6, "nodeType": "187", "endLine": 324, "endColumn": 57, "suggestions": "196"}, {"ruleId": "185", "severity": 1, "message": "197", "line": 475, "column": 6, "nodeType": "187", "endLine": 475, "endColumn": 26, "suggestions": "198"}, {"ruleId": "185", "severity": 1, "message": "199", "line": 481, "column": 6, "nodeType": "187", "endLine": 481, "endColumn": 21, "suggestions": "200"}, {"ruleId": "185", "severity": 1, "message": "201", "line": 33, "column": 6, "nodeType": "187", "endLine": 33, "endColumn": 8, "suggestions": "202"}, {"ruleId": "185", "severity": 1, "message": "203", "line": 40, "column": 6, "nodeType": "187", "endLine": 40, "endColumn": 37, "suggestions": "204"}, {"ruleId": "171", "severity": 1, "message": "205", "line": 14, "column": 10, "nodeType": "173", "messageId": "174", "endLine": 14, "endColumn": 21}, {"ruleId": "171", "severity": 1, "message": "206", "line": 31, "column": 9, "nodeType": "173", "messageId": "174", "endLine": 31, "endColumn": 22}, {"ruleId": "171", "severity": 1, "message": "207", "line": 226, "column": 11, "nodeType": "173", "messageId": "174", "endLine": 226, "endColumn": 25}, {"ruleId": "171", "severity": 1, "message": "208", "line": 447, "column": 9, "nodeType": "173", "messageId": "174", "endLine": 447, "endColumn": 24}, {"ruleId": "171", "severity": 1, "message": "209", "line": 464, "column": 9, "nodeType": "173", "messageId": "174", "endLine": 464, "endColumn": 24}, {"ruleId": "171", "severity": 1, "message": "210", "line": 479, "column": 9, "nodeType": "173", "messageId": "174", "endLine": 479, "endColumn": 22}, {"ruleId": "211", "severity": 1, "message": "212", "line": 28, "column": 1, "nodeType": "213", "endLine": 30, "endColumn": 3}, {"ruleId": "185", "severity": 1, "message": "214", "line": 277, "column": 6, "nodeType": "187", "endLine": 277, "endColumn": 66, "suggestions": "215"}, {"ruleId": "185", "severity": 1, "message": "216", "line": 558, "column": 6, "nodeType": "187", "endLine": 558, "endColumn": 76, "suggestions": "217"}, "no-unused-vars", "'downloadAsExcel' is assigned a value but never used.", "Identifier", "unusedVar", "'downloadAsCSV' is assigned a value but never used.", "'isLoggedIn' is assigned a value but never used.", "'userTier' is assigned a value but never used.", "'conversionsLeft' is assigned a value but never used.", "'handleInspect' is assigned a value but never used.", "'handleLogout' is assigned a value but never used.", ["218"], "'res' is assigned a value but never used.", "'extractedData' is assigned a value but never used.", "'setExtractedData' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'createNewTable' and 'tables.length'. Either include them or remove the dependency array.", "ArrayExpression", ["219"], "'pdfjs' is defined but never used.", ["220"], "'pdfjsLib' is defined but never used.", ["221"], "'numPages' is assigned a value but never used.", "'maxItemsInRow' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'renderPage'. Either include it or remove the dependency array.", ["222"], "React Hook useEffect has missing dependencies: 'pageRendering' and 'renderPage'. Either include them or remove the dependency array.", ["223"], "React Hook useEffect has missing dependencies: 'pageNumber' and 'renderPage'. Either include them or remove the dependency array.", ["224"], "React Hook useEffect has a missing dependency: 'tesseractWorker'. Either include it or remove the dependency array.", ["225"], "React Hook useEffect has missing dependencies: 'captureScreenshot' and 'isCapturing'. Either include them or remove the dependency array.", ["226"], "'pdfFileName' is assigned a value but never used.", "'renderTaskRef' is assigned a value but never used.", "'storedFileName' is assigned a value but never used.", "'handleMouseDown' is assigned a value but never used.", "'handleMouseMove' is assigned a value but never used.", "'handleMouseUp' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useCallback has a missing dependency: 'isTableMode'. Either include it or remove the dependency array.", ["227"], "React Hook useEffect has a missing dependency: 'location.state'. Either include it or remove the dependency array.", ["228"], {"kind": "229", "justification": "230"}, {"desc": "231", "fix": "232"}, {"kind": "229", "justification": "230"}, {"kind": "229", "justification": "230"}, {"desc": "233", "fix": "234"}, {"desc": "235", "fix": "236"}, {"desc": "237", "fix": "238"}, {"desc": "239", "fix": "240"}, {"desc": "241", "fix": "242"}, {"desc": "243", "fix": "244"}, {"desc": "245", "fix": "246"}, "directive", "", "Update the dependencies array to be: [createNewTable, tables.length]", {"range": "247", "text": "248"}, "Update the dependencies array to be: [pdfFile, onDocumentLoadSuccess, pageNumber, scale, renderPage]", {"range": "249", "text": "250"}, "Update the dependencies array to be: [pageNumber, pageRendering, pdfDoc, renderPage]", {"range": "251", "text": "252"}, "Update the dependencies array to be: [scale, pdfDoc, renderPage, pageNumber]", {"range": "253", "text": "254"}, "Update the dependencies array to be: [tesseractWorker]", {"range": "255", "text": "256"}, "Update the dependencies array to be: [captureScreenshot, isCapturing, selectionBox, tesseractWorker]", {"range": "257", "text": "258"}, "Update the dependencies array to be: [selectionBox, gridLines, isOcrEnabled, sendMessageToIframe, isTableMode]", {"range": "259", "text": "260"}, "Update the dependencies array to be: [loadPDF, sendMessageToIframe, extractedTables, pdfFile, selectionBox, location.state]", {"range": "261", "text": "262"}, [574, 576], "[createNewTable, tables.length]", [10562, 10613], "[pdfFile, onDocumentLoadSuccess, pageNumber, scale, renderPage]", [15839, 15859], "[pageNumber, pageRendering, pdfDoc, renderPage]", [15972, 15987], "[scale, pdfDoc, renderPage, pageNumber]", [1090, 1092], "[tesseractWorker]", [1288, 1319], "[captureScreenshot, isCapturing, selectionBox, tesseractWorker]", [9172, 9232], "[selectionBox, gridLines, isOcrEnabled, sendMessageToIframe, isTableMode]", [19649, 19719], "[loadPDF, sendMessageToIframe, extractedTables, pdfFile, selectionBox, location.state]"]