{"ast": null, "code": "var _jsxFileName = \"F:\\\\converterpdf\\\\frontend\\\\src\\\\pages\\\\ConversionResultsPage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useLocation } from 'react-router-dom';\nimport axios from 'axios'; // Import axios\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ConversionResultsPage() {\n  _s();\n  const location = useLocation();\n  const [extractedData, setExtractedData] = useState(null); // Use extractedData for the new structure\n  const [originalFilename, setOriginalFilename] = useState('');\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    const params = new URLSearchParams(location.search);\n    const uuid = params.get('uuids');\n    const filename = params.get('filename');\n    if (uuid) {\n      setOriginalFilename(filename || 'Uploaded File');\n      // Fetch extracted data from the backend using the UUID\n      axios.get(`/api/conversion-results/${uuid}`).then(res => {\n        console.log(\"Frontend received extracted data:\", res.data); // Add logging\n        setExtractedData(res.data); // Set the new data structure\n        setIsLoading(false);\n      }).catch(err => {\n        console.error('Error fetching extracted data:', err);\n        setError('Could not load extracted data.');\n        setIsLoading(false);\n      });\n    } else {\n      setError('No conversion ID provided.');\n      setIsLoading(false);\n    }\n  }, [location.search]); // Re-run effect if query parameters change\n\n  if (isLoading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Conversion Results\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Loading extracted data...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Conversion Results\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"error\",\n        children: error\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 51,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Handle both old unified_table format and new tables_data format\n  const tablesData = (extractedData === null || extractedData === void 0 ? void 0 : extractedData.tables_data) || (extractedData !== null && extractedData !== void 0 && extractedData.unified_table ? [extractedData.unified_table] : null);\n  if (!extractedData || !tablesData || tablesData.length === 0) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        children: \"Conversion Results\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"No table data available.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), extractedData && extractedData.status && /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Status: \", extractedData.status]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 51\n      }, this), extractedData && extractedData.error && /*#__PURE__*/_jsxDEV(\"p\", {\n        children: [\"Backend Error: \", extractedData.error]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 50\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Render all tables from tables_data using backend-provided headers and rows\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"container\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      children: \"Conversion Results\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Processed \", tablesData.length, \" tables from: \", originalFilename]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"table-container\",\n      children: tablesData.map((table, tableIdx) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '2rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n          children: [\"Table \", tableIdx + 1]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"results-table\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: table.headers && table.headers.map((header, idx) => /*#__PURE__*/_jsxDEV(\"th\", {\n                children: header\n              }, idx, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: table.rows && table.rows.map((row, rowIdx) => /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: row.map((cell, cellIdx) => /*#__PURE__*/_jsxDEV(\"td\", {\n                children: cell !== null ? String(cell) : ''\n              }, cellIdx, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 23\n              }, this))\n            }, rowIdx, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 13\n        }, this)]\n      }, tableIdx, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"download-options\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Download options will be available once the conversion logic is fully implemented.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 102,\n        columnNumber: 10\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n}\n_s(ConversionResultsPage, \"SkW9ulY2hE0jYBaiI0h+66RAaZU=\", false, function () {\n  return [useLocation];\n});\n_c = ConversionResultsPage;\nexport default ConversionResultsPage;\nvar _c;\n$RefreshReg$(_c, \"ConversionResultsPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useLocation", "axios", "jsxDEV", "_jsxDEV", "ConversionResultsPage", "_s", "location", "extractedData", "setExtractedData", "originalFilename", "setOriginalFilename", "isLoading", "setIsLoading", "error", "setError", "params", "URLSearchParams", "search", "uuid", "get", "filename", "then", "res", "console", "log", "data", "catch", "err", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "tablesData", "tables_data", "unified_table", "length", "status", "map", "table", "tableIdx", "style", "marginBottom", "headers", "header", "idx", "rows", "row", "rowIdx", "cell", "cellIdx", "String", "_c", "$RefreshReg$"], "sources": ["F:/converterpdf/frontend/src/pages/ConversionResultsPage.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\r\nimport { useLocation } from 'react-router-dom';\r\nimport axios from 'axios'; // Import axios\r\n\r\nfunction ConversionResultsPage() {\r\n  const location = useLocation();\r\n  const [extractedData, setExtractedData] = useState(null); // Use extractedData for the new structure\r\n  const [originalFilename, setOriginalFilename] = useState('');\r\n  const [isLoading, setIsLoading] = useState(true);\r\n  const [error, setError] = useState(null);\r\n\r\n  useEffect(() => {\r\n    const params = new URLSearchParams(location.search);\r\n    const uuid = params.get('uuids');\r\n    const filename = params.get('filename');\r\n\r\n    if (uuid) {\r\n      setOriginalFilename(filename || 'Uploaded File');\r\n      // Fetch extracted data from the backend using the UUID\r\n      axios.get(`/api/conversion-results/${uuid}`)\r\n        .then(res => {\r\n          console.log(\"Frontend received extracted data:\", res.data); // Add logging\r\n          setExtractedData(res.data); // Set the new data structure\r\n          setIsLoading(false);\r\n        })\r\n        .catch(err => {\r\n          console.error('Error fetching extracted data:', err);\r\n          setError('Could not load extracted data.');\r\n          setIsLoading(false);\r\n        });\r\n    } else {\r\n      setError('No conversion ID provided.');\r\n      setIsLoading(false);\r\n    }\r\n  }, [location.search]); // Re-run effect if query parameters change\r\n\r\n  if (isLoading) {\r\n    return (\r\n      <div className=\"container\">\r\n        <h2>Conversion Results</h2>\r\n        <p>Loading extracted data...</p>\r\n        {/* Add a loading spinner here */}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (error) {\r\n    return (\r\n      <div className=\"container\">\r\n        <h2>Conversion Results</h2>\r\n        <p className=\"error\">{error}</p>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Handle both old unified_table format and new tables_data format\r\n  const tablesData = extractedData?.tables_data ||\r\n                    (extractedData?.unified_table ? [extractedData.unified_table] : null);\r\n\r\n  if (!extractedData || !tablesData || tablesData.length === 0) {\r\n    return (\r\n      <div className=\"container\">\r\n        <h2>Conversion Results</h2>\r\n        <p>No table data available.</p>\r\n        {extractedData && extractedData.status && <p>Status: {extractedData.status}</p>}\r\n        {extractedData && extractedData.error && <p>Backend Error: {extractedData.error}</p>}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Render all tables from tables_data using backend-provided headers and rows\r\n  return (\r\n    <div className=\"container\">\r\n      <h2>Conversion Results</h2>\r\n      <p>Processed {tablesData.length} tables from: {originalFilename}</p>\r\n      <div className=\"table-container\">\r\n        {tablesData.map((table, tableIdx) => (\r\n          <div key={tableIdx} style={{ marginBottom: '2rem' }}>\r\n            <h4>Table {tableIdx + 1}</h4>\r\n            <table className=\"results-table\">\r\n              <thead>\r\n                <tr>\r\n                  {table.headers && table.headers.map((header, idx) => (\r\n                    <th key={idx}>{header}</th>\r\n                  ))}\r\n                </tr>\r\n              </thead>\r\n              <tbody>\r\n                {table.rows && table.rows.map((row, rowIdx) => (\r\n                  <tr key={rowIdx}>\r\n                    {row.map((cell, cellIdx) => (\r\n                      <td key={cellIdx}>{cell !== null ? String(cell) : ''}</td>\r\n                    ))}\r\n                  </tr>\r\n                ))}\r\n              </tbody>\r\n            </table>\r\n          </div>\r\n        ))}\r\n      </div>\r\n      <div className=\"download-options\">\r\n         <p>Download options will be available once the conversion logic is fully implemented.</p>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default ConversionResultsPage;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,KAAK,MAAM,OAAO,CAAC,CAAC;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAE3B,SAASC,qBAAqBA,CAAA,EAAG;EAAAC,EAAA;EAC/B,MAAMC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACO,aAAa,EAAEC,gBAAgB,CAAC,GAAGT,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACU,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC5D,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,IAAI,CAAC;EAExCD,SAAS,CAAC,MAAM;IACd,MAAMiB,MAAM,GAAG,IAAIC,eAAe,CAACV,QAAQ,CAACW,MAAM,CAAC;IACnD,MAAMC,IAAI,GAAGH,MAAM,CAACI,GAAG,CAAC,OAAO,CAAC;IAChC,MAAMC,QAAQ,GAAGL,MAAM,CAACI,GAAG,CAAC,UAAU,CAAC;IAEvC,IAAID,IAAI,EAAE;MACRR,mBAAmB,CAACU,QAAQ,IAAI,eAAe,CAAC;MAChD;MACAnB,KAAK,CAACkB,GAAG,CAAC,2BAA2BD,IAAI,EAAE,CAAC,CACzCG,IAAI,CAACC,GAAG,IAAI;QACXC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEF,GAAG,CAACG,IAAI,CAAC,CAAC,CAAC;QAC5DjB,gBAAgB,CAACc,GAAG,CAACG,IAAI,CAAC,CAAC,CAAC;QAC5Bb,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,CAAC,CACDc,KAAK,CAACC,GAAG,IAAI;QACZJ,OAAO,CAACV,KAAK,CAAC,gCAAgC,EAAEc,GAAG,CAAC;QACpDb,QAAQ,CAAC,gCAAgC,CAAC;QAC1CF,YAAY,CAAC,KAAK,CAAC;MACrB,CAAC,CAAC;IACN,CAAC,MAAM;MACLE,QAAQ,CAAC,4BAA4B,CAAC;MACtCF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACN,QAAQ,CAACW,MAAM,CAAC,CAAC,CAAC,CAAC;;EAEvB,IAAIN,SAAS,EAAE;IACb,oBACER,OAAA;MAAKyB,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB1B,OAAA;QAAA0B,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3B9B,OAAA;QAAA0B,QAAA,EAAG;MAAyB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAE7B,CAAC;EAEV;EAEA,IAAIpB,KAAK,EAAE;IACT,oBACEV,OAAA;MAAKyB,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB1B,OAAA;QAAA0B,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3B9B,OAAA;QAAGyB,SAAS,EAAC,OAAO;QAAAC,QAAA,EAAEhB;MAAK;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC;EAEV;;EAEA;EACA,MAAMC,UAAU,GAAG,CAAA3B,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE4B,WAAW,MAC1B5B,aAAa,aAAbA,aAAa,eAAbA,aAAa,CAAE6B,aAAa,GAAG,CAAC7B,aAAa,CAAC6B,aAAa,CAAC,GAAG,IAAI,CAAC;EAEvF,IAAI,CAAC7B,aAAa,IAAI,CAAC2B,UAAU,IAAIA,UAAU,CAACG,MAAM,KAAK,CAAC,EAAE;IAC5D,oBACElC,OAAA;MAAKyB,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxB1B,OAAA;QAAA0B,QAAA,EAAI;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3B9B,OAAA;QAAA0B,QAAA,EAAG;MAAwB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,EAC9B1B,aAAa,IAAIA,aAAa,CAAC+B,MAAM,iBAAInC,OAAA;QAAA0B,QAAA,GAAG,UAAQ,EAACtB,aAAa,CAAC+B,MAAM;MAAA;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,EAC9E1B,aAAa,IAAIA,aAAa,CAACM,KAAK,iBAAIV,OAAA;QAAA0B,QAAA,GAAG,iBAAe,EAACtB,aAAa,CAACM,KAAK;MAAA;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjF,CAAC;EAEV;;EAEA;EACA,oBACE9B,OAAA;IAAKyB,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxB1B,OAAA;MAAA0B,QAAA,EAAI;IAAkB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC3B9B,OAAA;MAAA0B,QAAA,GAAG,YAAU,EAACK,UAAU,CAACG,MAAM,EAAC,gBAAc,EAAC5B,gBAAgB;IAAA;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACpE9B,OAAA;MAAKyB,SAAS,EAAC,iBAAiB;MAAAC,QAAA,EAC7BK,UAAU,CAACK,GAAG,CAAC,CAACC,KAAK,EAAEC,QAAQ,kBAC9BtC,OAAA;QAAoBuC,KAAK,EAAE;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAd,QAAA,gBAClD1B,OAAA;UAAA0B,QAAA,GAAI,QAAM,EAACY,QAAQ,GAAG,CAAC;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC7B9B,OAAA;UAAOyB,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC9B1B,OAAA;YAAA0B,QAAA,eACE1B,OAAA;cAAA0B,QAAA,EACGW,KAAK,CAACI,OAAO,IAAIJ,KAAK,CAACI,OAAO,CAACL,GAAG,CAAC,CAACM,MAAM,EAAEC,GAAG,kBAC9C3C,OAAA;gBAAA0B,QAAA,EAAegB;cAAM,GAAZC,GAAG;gBAAAhB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAc,CAC3B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR9B,OAAA;YAAA0B,QAAA,EACGW,KAAK,CAACO,IAAI,IAAIP,KAAK,CAACO,IAAI,CAACR,GAAG,CAAC,CAACS,GAAG,EAAEC,MAAM,kBACxC9C,OAAA;cAAA0B,QAAA,EACGmB,GAAG,CAACT,GAAG,CAAC,CAACW,IAAI,EAAEC,OAAO,kBACrBhD,OAAA;gBAAA0B,QAAA,EAAmBqB,IAAI,KAAK,IAAI,GAAGE,MAAM,CAACF,IAAI,CAAC,GAAG;cAAE,GAA3CC,OAAO;gBAAArB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAyC,CAC1D;YAAC,GAHKgB,MAAM;cAAAnB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAIX,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GAnBAQ,QAAQ;QAAAX,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoBb,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eACN9B,OAAA;MAAKyB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC9B1B,OAAA;QAAA0B,QAAA,EAAG;MAAkF;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAAC5B,EAAA,CArGQD,qBAAqB;EAAA,QACXJ,WAAW;AAAA;AAAAqD,EAAA,GADrBjD,qBAAqB;AAuG9B,eAAeA,qBAAqB;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}