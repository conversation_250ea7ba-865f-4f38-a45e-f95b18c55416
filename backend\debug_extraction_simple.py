#!/usr/bin/env python3
"""
Simple debug script to test what's being extracted
"""
import sys
import os
import json

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from pdf_table_extractor import extract_tables_from_pdf, unify_tables

def debug_simple():
    """Simple debug of PDF extraction"""
    print("=== DEBUGGING PDF EXTRACTION ===")
    
    # Look for uploaded PDF files
    uploads_dir = "uploads"
    if os.path.exists(uploads_dir):
        pdf_files = [f for f in os.listdir(uploads_dir) if f.endswith('.pdf')]
        if pdf_files:
            pdf_path = os.path.join(uploads_dir, pdf_files[0])
            print(f"Found PDF: {pdf_path}")
            
            # Extract tables
            print("\n1. Extracting tables...")
            extracted_tables_data = extract_tables_from_pdf(pdf_path)
            
            if extracted_tables_data:
                print(f"   ✅ Extracted {len(extracted_tables_data)} tables")
                
                # Show first table structure
                first_table = extracted_tables_data[0]
                print(f"   First table has {len(first_table['data'])} rows")
                print(f"   First few rows:")
                for i, row in enumerate(first_table['data'][:3]):
                    print(f"     Row {i}: {row}")
                
                # Unify tables
                print("\n2. Unifying tables...")
                headers, rows = unify_tables(extracted_tables_data)
                
                if headers:
                    print(f"   ✅ Headers: {headers}")
                    print(f"   ✅ Got {len(rows)} unified rows")
                    
                    # Show first few rows
                    print(f"   First few unified rows:")
                    for i, row in enumerate(rows[:3]):
                        print(f"     Row {i}: {row}")
                        
                    # Check for numeric values
                    print("\n3. Checking for numeric values...")
                    debit_idx = headers.index("Debit") if "Debit" in headers else -1
                    credit_idx = headers.index("Credit") if "Credit" in headers else -1
                    balance_idx = headers.index("Balance") if "Balance" in headers else -1
                    
                    print(f"   Debit column index: {debit_idx}")
                    print(f"   Credit column index: {credit_idx}")
                    print(f"   Balance column index: {balance_idx}")
                    
                    numeric_found = False
                    for i, row in enumerate(rows[:5]):
                        if debit_idx >= 0 and len(row) > debit_idx and row[debit_idx]:
                            print(f"   Row {i} Debit: {row[debit_idx]} (type: {type(row[debit_idx])})")
                            numeric_found = True
                        if credit_idx >= 0 and len(row) > credit_idx and row[credit_idx]:
                            print(f"   Row {i} Credit: {row[credit_idx]} (type: {type(row[credit_idx])})")
                            numeric_found = True
                        if balance_idx >= 0 and len(row) > balance_idx and row[balance_idx]:
                            print(f"   Row {i} Balance: {row[balance_idx]} (type: {type(row[balance_idx])})")
                            numeric_found = True
                    
                    if not numeric_found:
                        print("   ❌ NO NUMERIC VALUES FOUND!")
                        print("   Checking descriptions for patterns...")
                        desc_idx = 1  # Description is usually column 1
                        for i, row in enumerate(rows[:3]):
                            if len(row) > desc_idx and row[desc_idx]:
                                desc = row[desc_idx]
                                print(f"   Row {i} Description: '{desc}'")
                                
                                # Test our patterns
                                import re
                                patterns = [
                                    r'Rs\.?\s*(\d+(?:,\d{3})*(?:\.\d{2})?)',
                                    r'\s(\d+(?:,\d{3})*\.\d{2})$',
                                    r'\s(\d{3,6})$'
                                ]
                                
                                for pattern in patterns:
                                    matches = re.findall(pattern, desc)
                                    if matches:
                                        print(f"     ✅ Pattern '{pattern}' found: {matches}")
                                        break
                                else:
                                    print(f"     ❌ No amount patterns found")
                    else:
                        print("   ✅ NUMERIC VALUES FOUND!")
                        
                else:
                    print("   ❌ No headers identified")
            else:
                print("   ❌ No tables extracted")
        else:
            print("No PDF files found in uploads directory")
    else:
        print("Uploads directory not found")

if __name__ == "__main__":
    debug_simple()
